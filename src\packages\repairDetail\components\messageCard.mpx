<template>
  <view
    class="message-card {{hasReplies ? 'has-replies' : ''}}"
    style="{{isReply ? 'padding-left: 32px;' : ''}}"
  >
    <view class="message-content">
      <view class="message-user">
        <view class="user-avatar">
          <image src="{{userInfo.avatar}}" class="avatar-image" />
        </view>
        <view class="user-info">
          <view class="header-top">
            <view class="username">{{ comment.realName }}</view>
            <view class="timestamp">{{ commentTime }}</view>
            <view wx:if="{{comment.replyToRealName}}" class="reply-to">
              <text class="reply-text">回复 {{ comment.replyToRealName }}</text>
            </view>
          </view>
          <view class="header-bottom">
            <view wx:if="{{comment.type}}" class="user-type-tag">
              <text class="tag-text" style="{{userInfo.tagStyle}}">{{
                userInfo.tag
              }}</text>
            </view>
          </view>
        </view>
      </view>
      <view class="message-text" wx:if="{{comment.content}}">
        <text class="text-content">{{ comment.content }}</text>
      </view>
      <view
        class="message-attachments"
        wx:if="{{formattedAttachments && formattedAttachments.length > 0}}"
      >
        <preview-media
          mediaList="{{formattedAttachments}}"
          wx:key="{{comment.id}}-attachments"
        ></preview-media>
      </view>
      <view class="message-actions">
        <view class="reply-btn" bindtap="handleReply">
          <image src="{{TechMaintainUrl.Reply}}" class="reply-icon" />
          <view class="reply-text">回复</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { TechMaintainUrl } from 'shared/assets/imageUrl';

  interface Attachment {
    type: string;
    fileKey: string;
    url: string;
  }

  interface Comment {
    id: number;
    type: string;
    realName: string;
    content: string;
    createTime: string;
    attachment: Attachment[];
    replyToRealName?: string;
    replyToCommentId?: number;
  }

  createComponent({
    properties: {
      comment: {
        type: Object,
        value: {}
      },
      isReply: {
        type: Boolean,
        value: false
      }
    },
    data: {
      TechMaintainUrl: TechMaintainUrl
    },
    computed: {
      hasReplies() {
        return this.comment.replies && this.comment.replies.length > 0;
      },
      userInfo() {
        const type = this.comment.type;
        switch (type) {
          case 'OEM':
            return {
              avatar: TechMaintainUrl.FactoryAvatar,
              tag: '车厂',
              tagStyle: 'background: #fff7e6; color: #fa8c16;' // 黄色
            };
          case 'SERVICE_STATION':
            return {
              avatar: TechMaintainUrl.StationAvator,
              tag: '服务站',
              tagStyle: 'background: #f6ffed; color: #52c41a;' // 绿色
            };
          case 'JD':
            return {
              avatar: TechMaintainUrl.JdAvatar,
              tag: '京东',
              tagStyle: 'background: #fff1f0; color: #ff4d4f;' // 红色
            };
          default:
            return {
              avatar: TechMaintainUrl.JdAvatar, // 默认使用京东头像
              tag: '',
              tagStyle: ''
            };
        }
      },
      commentTime() {
        return this.formatTime(this.comment.createTime);
      },
      formattedAttachments() {
        const attachments = this.comment?.attachment;
        if (
          !attachments ||
          !Array.isArray(attachments) ||
          attachments.length === 0
        ) {
          return [];
        }
        return attachments.map(item => ({
          type: item.type === 'video' ? 'video' : 'image',
          url: item.url,
          locationName: item.type === 'video' ? '视频附件' : '图片附件'
        }));
      }
    },
    methods: {
      formatTime(timeStr: string) {
        if (!timeStr) return '';
        // 处理iOS日期格式兼容性问题
        let formattedTimeStr = timeStr;
        if (/^\d{4}-\d{2}-\d{2}( \d{2}:\d{2}:\d{2})?$/.test(timeStr)) {
          formattedTimeStr = timeStr.replace(/-/g, '/');
        }
        const commentDate = new Date(formattedTimeStr);
        if (isNaN(commentDate.getTime())) {
          console.warn('Invalid date format:', timeStr);
          return timeStr;
        }
        const now = new Date();
        const nowDateOnly = new Date(
          now.getFullYear(),
          now.getMonth(),
          now.getDate()
        );
        const commentDateOnly = new Date(
          commentDate.getFullYear(),
          commentDate.getMonth(),
          commentDate.getDate()
        );
        const dayDiff = Math.floor(
          (nowDateOnly.getTime() - commentDateOnly.getTime()) /
            (1000 * 60 * 60 * 24)
        );
        const hour = String(commentDate.getHours()).padStart(2, '0');
        const minute = String(commentDate.getMinutes()).padStart(2, '0');
        console.log('时间差:', dayDiff, '小时:', hour, '分钟:', minute);
        if (dayDiff === 0) {
          return `${hour}:${minute}`;
        } else if (dayDiff === 1) {
          return `昨天 ${hour}:${minute}`;
        } else {
          const month = String(commentDate.getMonth() + 1).padStart(2, '0');
          const day = String(commentDate.getDate()).padStart(2, '0');
          return `${month}-${day} ${hour}:${minute}`;
        }
      },
      handleReply() {
        this.triggerEvent('reply', {
          commentId: this.comment.id,
          userName: this.comment.realName
        });
      }
    }
  });
</script>

<style lang="scss">
  .message-card {
    background: #ffffff;
    border-radius: 8px;
    padding: 8px;
    &.has-replies {
      border-bottom: 1px solid #f0f0f0;
      padding-bottom: 8px;
    }

    .message-content {
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      .message-user {
        display: flex;
        justify-content: flex-start;
        width: 100%;

        .user-avatar {
          width: 40px;
          height: 40px;
          margin-right: 8px;

          .avatar-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .user-info {
          display: flex;
          flex-direction: column;
          margin-bottom: 8px;

          .header-top {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            width: 100%;
            margin-bottom: 4px;

            .username {
              font-size: 14px;
              font-weight: 500;
              color: #333333;
              margin-right: 4px;
            }
            .timestamp {
              font-size: 12px;
              color: #999999;
              margin-right: 4px;
            }
            .reply-to {
              .reply-text {
                font-size: 10px;
                color: #808080;
                background: #f5f5f6;
                padding: 2px 6px;
                border-radius: 5px;
              }
            }
          }

          .header-bottom {
            display: flex;
            align-items: center;
            width: 100%;

            .user-type-tag {
              .tag-text {
                padding: 2px 6px;
                border-radius: 12px;
                font-size: 12px;
                font-weight: 400;
                line-height: 1.5;
                display: inline-block;
              }
            }
          }

          .status-icon {
            width: 16px;
            height: 16px;

            .icon-image {
              width: 100%;
              height: 100%;
              object-fit: contain;
            }
          }
        }
      }
      .message-text {
        margin-bottom: 12px;
        width: 100%;

        .text-content {
          font-size: 14px;
          color: #333333;
          line-height: 1.5;
          word-break: break-all;
          display: block;
        }
      }

      .message-attachments {
        width: 100%;
        margin-bottom: 12px;
      }

      .message-actions {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;

        .reply-btn {
          display: flex;
          align-items: center;
          padding: 6px 12px;
          border-radius: 16px;
          .reply-icon {
            width: 14px;
            height: 14px;
            object-fit: contain;
          }
          .reply-text {
            margin-left: 4px;
            font-size: 12px;
            color: #666666;
          }
        }
      }
    }
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "preview-media": "/shared/ui/previewMedia.mpx"
    }
  }
</script>