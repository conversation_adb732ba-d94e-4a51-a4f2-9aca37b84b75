<template>
  <view class="tech-maintenance-container">
    <navi-bar
      title="服务站维修"
      showReturn="{{true}}"
      backUrl="{{backUrl}}"
      showAddress="{{false}}"
    ></navi-bar>

    <view class="menu-container">
      <t-tabs
        class="menu"
        value="{{activeTabKey}}"
        space-evenly="{{true}}"
        bind:change="onTabsChange"
      >
        <t-tab-panel
          class="menu-item"
          wx:for="{{tabsList}}"
          wx:key="key"
          label="{{item.name}}"
          value="{{item.key}}"
          badge-props="{{ { count: item.count, offset: ['4px', '4px'] } }}"
        />
      </t-tabs>
    </view>

    <scroll-view
      class="content-container"
      style="height: calc(100vh - {{naviHeight}}px - {{tabsHeight}}px - 16px);"
      scroll-y
      type="list"
      show-scrollbar="{{false}}"
      enable-back-to-top="{{true}}"
    >
      <vehicle-info-card
        wx:for="{{contentList}}"
        wx:key="number"
        vehicleInfo="{{item}}"
        data-item="{{item}}"
        bindtap="handleRepairOrderClick"
      >
        <view slot="extra-base-info" class="order-number">
          <text class="number-text">{{ item.number }}</text>
          <copy-icon code="{{item.number}}"></copy-icon>
        </view>
        <view slot="extend-info" class="order-content">
          <view class="order-description">
            问题描述：<text class="description-text">{{
              item.description
            }}</text>
          </view>
          <view class="appointment-time">
            预约维修时间：<text class="black-text">{{
              item.appointmentRepairTime
            }}</text>
          </view>
        </view>
      </vehicle-info-card>
      <view wx:if="{{contentList.length === 0}}" class="empty-state">
        <text class="empty-text">暂无维修单</text>
      </view>
    </scroll-view>
  </view>
</template>

<script lang="ts">
  import { createPage } from '@mpxjs/core';
  import TechMaintenanceApi from 'shared/api/techMaintenance';
  import { HTTPSTATUSCODE } from '../../shared/api/fetch';
  import {
    RepairOrderStatus,
    RepairStatusMenu,
    IsInfluenceOperation
  } from 'shared/utils/constant';

  interface RepairOrder {
    deviceName: string;
    createTime: string;
    stationBaseId: number;
    stationBaseName: string | null;
    number: string;
    description: string | undefined;
    appointmentRepairTime: string | null;
    isInfluenceOperation: number;
  }
  interface RepairOrderMap {
    [RepairOrderStatus.UNDER_REPAIR]: RepairOrder[];
    [RepairOrderStatus.PENDING_ACCEPTANCE]: RepairOrder[];
    [RepairOrderStatus.ACCEPTANCE_REJECTED]: RepairOrder[];
    [RepairOrderStatus.COMPLETED]: RepairOrder[];
  }
  interface Data {
    fetchApi: TechMaintenanceApi;
    activeTabKey: RepairOrderStatus;
    repairOrderMap: RepairOrderMap;
    tabs: any;
    backUrl: string;
    loading: boolean;
    naviHeight: number;
    tabsHeight: number;
  }

  createPage<Data>({
    data: {
      fetchApi: new TechMaintenanceApi(),
      activeTabKey: RepairOrderStatus.UNDER_REPAIR,
      repairOrderMap: {
        [RepairOrderStatus.UNDER_REPAIR]: [],
        [RepairOrderStatus.PENDING_ACCEPTANCE]: [],
        [RepairOrderStatus.ACCEPTANCE_REJECTED]: [],
        [RepairOrderStatus.COMPLETED]: []
      },
      tabs: RepairStatusMenu,
      backUrl: '/pages/workbench/index',
      loading: false,
      naviHeight: 100,
      tabsHeight: 44
    },
    computed: {
      tabsList() {
        return Object.values(this.tabs);
      },
      contentList() {
        return this.repairOrderMap[this.activeTabKey] || [];
      },
      scrollViewHeight() {
        const screenHeight = wx.getSystemInfoSync().screenHeight;
        return screenHeight - this.naviHeight - this.tabsHeight;
      }
    },
    onLoad() {
      wx.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: '#ffffff'
      });

      // 调试：检查当前页面路径和预加载状态
      const currentPage = getCurrentPages()[getCurrentPages().length - 1];
      console.log('技术维护页面路径:', currentPage.route);
      console.log('预加载配置中的路径: pages/techMaintenance/index');
      console.log('路径是否匹配:', currentPage.route === 'pages/techMaintenance/index');

      // 延迟检查预加载效果
      setTimeout(() => {
        console.log('页面加载完成，测试分包预加载效果...');
        const testStartTime = Date.now();
        wx.navigateTo({
          url: '/repairDetail/pages/repairDetail/index?number=PRELOAD_TEST',
          success: () => {
            const testLoadTime = Date.now() - testStartTime;
            console.log(`预加载测试 - 分包加载耗时: ${testLoadTime}ms`);
            // 立即返回
            setTimeout(() => wx.navigateBack(), 100);
          },
          fail: (err) => {
            console.error('预加载测试失败:', err);
          }
        });
      }, 2000); // 等待2秒让预加载有时间完成

      this.calculateHeights();
      this.getRepairOrderList();
    },
    methods: {
      calculateHeights() {
        const systemInfo = wx.getSystemInfoSync();
        const naviHeight = systemInfo.statusBarHeight + 44; // 状态栏 + 标题栏
        this.setData({
          naviHeight,
          tabsHeight: 44 // tabs标准高度
        });

        // 设置CSS变量
        const app = getApp();
        if (app.globalData) {
          app.globalData.naviHeight = naviHeight;
        }
      },
      getInfluenceOperation(value: any) {
        if (value === null || value === undefined || isNaN(value)) {
          return null;
        }
        switch (Number(value)) {
          case IsInfluenceOperation.INFLUENCE:
            return {
              label: '影响运营',
              labelColor: 'red',
              imageWidth: '70px'
            };
          case IsInfluenceOperation.NO_INFLUENCE:
            return {
              label: '不影响运营',
              labelColor: 'green',
              imageWidth: '80px'
            };
          default:
            return null;
        }
      },
      formatRepairOrderList(list: RepairOrder[]) {
        return list.map(item => ({
          ...item,
          iconInfo: this.getInfluenceOperation(item.isInfluenceOperation),
          vehicleName: item.deviceName ?? '-',
          time: item.createTime ?? '-',
          stationName: item.stationBaseName ?? '-',
          number: item.number ?? '-',
          description: item.description ?? '-',
          appointmentRepairTime: item.appointmentRepairTime ?? '-'
        }));
      },
      async getRepairOrderList() {
        // 显示loading
        this.setData({ loading: true });
        wx.showLoading({
          title: '加载中...',
          mask: true
        });
        try {
          let data = wx.getStorageSync('repairOrderList');
          if (!Array.isArray(data)) {
            const res = await this.fetchApi.getServiceStationRequireList();
            if (res.code === HTTPSTATUSCODE.Success) {
              data = res.data;
              wx.setStorageSync('repairOrderList', data);
            } else {
              wx.showToast({
                title: res.message || '获取数据失败',
                icon: 'none',
                duration: 2000
              });
              throw new Error(res.message || '获取维修单列表失败');
            }
          }
          const newTabs = { ...this.tabs };
          const statusMap = {
            [RepairOrderStatus.UNDER_REPAIR]: this.formatRepairOrderList(
              data.maintainingList || []
            ),
            [RepairOrderStatus.PENDING_ACCEPTANCE]: this.formatRepairOrderList(
              data.awaitingAcceptanceList || []
            ),
            [RepairOrderStatus.ACCEPTANCE_REJECTED]: this.formatRepairOrderList(
              data.acceptanceRejectedList || []
            ),
            [RepairOrderStatus.COMPLETED]: this.formatRepairOrderList(
              data.completedList || []
            )
          };
          // 只有维修中和验收驳回需要显示维修单的计数
          newTabs[RepairOrderStatus.UNDER_REPAIR].count =
            statusMap[RepairOrderStatus.UNDER_REPAIR].length;
          newTabs[RepairOrderStatus.ACCEPTANCE_REJECTED].count =
            statusMap[RepairOrderStatus.ACCEPTANCE_REJECTED].length;
          this.setData({
            repairOrderMap: statusMap,
            tabs: newTabs
          });
        } catch (error) {
          console.error('获取维修单列表失败:', error);
        } finally {
          this.setData({ loading: false });
          wx.hideLoading();
        }
      },
      handleRepairOrderClick(event: any) {
        console.log('handleRepairOrderClick 被调用了', event);
        const item = event.currentTarget.dataset.item;
        console.log('点击维修单:', item);

        // 打印当前页面栈
        const pages = getCurrentPages();
        console.log('跳转前页面栈:', pages.map(p => p.route));

        if (item && item.number) {
          // 显示loading
          wx.showLoading({
            title: '加载中...',
            mask: true
          });

          const targetUrl = `/repairDetail/pages/repairDetail/index?number=${item.number}`;
          console.log('准备跳转到:', targetUrl);

          wx.navigateTo({
            url: targetUrl,
            success: res => {
              console.log('跳转成功:', res);
              // 跳转成功后检查页面栈
              setTimeout(() => {
                const newPages = getCurrentPages();
                console.log('跳转后页面栈:', newPages.map(p => p.route));
                wx.hideLoading();
              }, 100);
            },
            fail: err => {
              console.error('跳转失败:', err);
              console.error('错误详情:', JSON.stringify(err));
              wx.hideLoading();
              wx.showToast({
                title: `跳转失败: ${err.errMsg || '未知错误'}`,
                icon: 'none',
                duration: 3000
              });
            }
          });
        } else {
          console.error('item 或 item.number 不存在:', item);
          wx.showToast({
            title: 'item数据不存在',
            icon: 'none'
          });
        }
      },
      onTabsChange(event: any) {
        const { value } = event.detail;
        this.setData({
          activeTabKey: value
        });
      }
    }
  });
</script>

<style lang="scss">
  page {
    height: 100%;
    width: 100%;
    overflow: hidden;
  }

  .tech-maintenance-container {
    height: 100%;
    width: 100%;
    background: rgba(245, 245, 245, 1);

    .menu-container {
      display: flex;
      flex-direction: column;
      .menu {
        --td-tab-item-active-color: rgba(250, 44, 25, 1);
        --td-badge-bg-color: rgba(252, 55, 55, 1);
        .t-tabs__item-inner--active {
          --td-badge-content-text-color: rgba(250, 44, 25, 1);
        }
      }
    }

    .content-container {
      width: 100%;
      padding: 8px 12px;
      box-sizing: border-box;
    }

    .empty-state {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;

      .empty-text {
        font-size: 16px;
        color: #999999;
      }
    }

    .order-number {
      display: flex;
      align-items: center;
      margin: 8px 0;

      .number-text {
        font-size: 14px;
        font-weight: 500;
        color: #808080;
      }
    }

    .order-content {
      font-size: 14px;
      .order-description {
        color: #646464;
        margin-bottom: 8px;
        .description-text {
          color: #000000;
          padding-left: 8px;
        }
      }
      .appointment-time {
        color: #666666;
        .black-text {
          color: #000000;
        }
      }
    }
  }
</style>

<script type="application/json">
  {
    "usingComponents": {
      "navi-bar": "shared/ui/naviBar.mpx",
      "vehicle-info-card": "shared/ui/vehicleInfoCard.mpx",
      "copy-icon": "shared/ui/copyIcon.mpx",
      "t-tabs": "tdesign-miniprogram/tabs/tabs",
      "t-tab-panel": "tdesign-miniprogram/tab-panel/tab-panel"
    },
    "navigationStyle": "custom",
    "renderer": "webview"
  }
</script>