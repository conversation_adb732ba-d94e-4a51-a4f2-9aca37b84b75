<template>
  <view class="report-info-container">
    <!-- 车辆信息区域 -->
    <view class="vehicle-info-section">
      <view class="vehicle-header">
        <text class="vehicle-name">{{ repairDetail.deviceName }}</text>
        <text class="business-unit">{{ repairDetail.stationName }}</text>
      </view>

      <view class="vehicle-detail-row">
        <text class="detail-label">车架号：</text>
        <text class="detail-value">{{ repairDetail.serialNo }}</text>
      </view>

      <view class="vehicle-detail-row">
        <text class="detail-label">站点地址：</text>
        <text class="detail-value">{{ repairDetail.stationBaseAddress }}</text>
      </view>
    </view>

    <!-- 报告人信息区域 -->
    <reporter-info
      reporterInfo="{{repairDetail.reporterInfo}}"
      orderNumber="{{repairDetail.number}}"
    ></reporter-info>

    <!-- 维修单描述 -->
    <view class="description-section">
      <text class="description-text">{{ repairDetail.description }}</text>
      <view
        class="media-section"
        wx:if="{{repairDetail.attachments && repairDetail.attachments.length > 0}}"
      >
        <preview-media mediaList="{{repairDetail.attachments}}"></preview-media>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';

  interface ReporterInfo {
    name: string;
    email: string;
    phone: string;
    avatar: string;
  }

  interface RepairDetail {
    number: string;
    deviceName: string;
    stationName: string;
    serialNo: string;
    stationBaseAddress: string;
    reporterInfo: ReporterInfo;
    description: string;
    appointmentRepairTime: string;
    attachments: Array<{
      type: 'image' | 'video';
      url: string;
      locationName?: string;
    }>;
  }

  createComponent({
    properties: {
      repairDetail: {
        type: Object,
        value: {}
      }
    },
    data: {},
    methods: {}
  });
</script>

<style lang="scss">
  .report-info-container {
    background: #fff;
    width: 100%;
    padding: 16px 0;
    box-sizing: border-box;

    .vehicle-info-section {
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid #f0f0f0;

      .vehicle-header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        .vehicle-name {
          font-size: 18px;
          font-weight: 600;
          color: #333333;
          margin-right: 12px;
        }

        .business-unit {
          font-size: 12px;
          color: #fb6419;
          background: #fff1eb;
          padding: 4px 8px;
          border-radius: 4px;
        }
      }

      .vehicle-detail-row {
        display: flex;
        margin-bottom: 12px;
        align-items: flex-start;

        .detail-label {
          font-size: 14px;
          color: #666666;
          margin-right: 8px;
          flex-shrink: 0;
          min-width: 80px;
        }

        .detail-value {
          font-size: 14px;
          color: #333333;
          flex: 1;
          line-height: 1.5;
          word-wrap: break-word;
        }
      }
    }

    .description-section {
      margin-top: 24px;
      margin-bottom: 100px; // 增加底部间距，确保内容足够长时能滚动

      .description-text {
        font-size: 14px;
        color: #333333;
        line-height: 1.6;
        word-wrap: break-word;
        white-space: pre-wrap;
        margin-bottom: 20px;
        min-height: 100px; // 设置最小高度确保有内容可滚动
      }

      .media-section {
        margin-top: 20px;
        margin-bottom: 40px;
      }
    }
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "reporter-info": "./reporterInfo.mpx",
      "preview-media": "/shared/ui/previewMedia2.mpx"
    }
  }
</script>