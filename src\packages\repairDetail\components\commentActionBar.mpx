<template>
  <!-- 收起状态 -->
  <view
    class="action-bar collapsed"
    wx:if="{{!isInputExpanded}}"
    style="transform: translateY({{keyboardHeight > 0 ? -keyboardHeight : 0}}px); transition: transform 0.3s ease;"
  >
    <view class="action-content">
      <view class="input-section" bindtap="handleExpandInputBar"
        >输入留言
      </view>
      <view class="action-buttons">
        <view class="collapsed-btn notice" bindtap="handleNotification">
          通知
        </view>
        <view class="collapsed-btn permission" bindtap="handlePermission">
          权限设置
        </view>
        <view class="max-avatar" bindtap="handleMaxSummary">
          <image src="{{TechMaintainUrl.Max}}" class="avatar-image" />
        </view>
      </view>
    </view>
  </view>
  <comment-typing
    visible="{{isInputExpanded}}"
    bind:close="handleExpandInputBar"
    replyTo="{{replyTo}}"
    bind:cancelReply="handleCancelReply"
    bind:sendComment="handleSendComment"
  />
  <permission-setting
    visible="{{isNoticeExpanded}}"
    bind:close="handlePermission"
  />
  <max-summary
    visible="{{isMaxSummaryExpanded}}"
    bind:close="handleMaxSummary"
  />
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { TechMaintainUrl } from 'shared/assets/imageUrl';

  createComponent({
    properties: {
      replyTo: {
        type: Object,
        value: {}
      }
    },
    data: {
      TechMaintainUrl,
      isInputExpanded: false,
      isNoticeExpanded: false,
      isMaxSummaryExpanded: false
    },
    watch: {
      replyTo(newVal: any) {
        this.handleExpandInputBar();
      }
    },
    methods: {
      handleCancelReply() {
        this.triggerEvent('cancelReply');
      },
      handleExpandInputBar() {
        this.setData({
          isInputExpanded: !this.data.isInputExpanded
        });
      },
      handleSendComment(e: any) {
        this.triggerEvent('sendComment', e.detail);
      },
      handleNotification() {
        this.triggerEvent('notification');
      },
      handlePermission() {
        console.log('进入函数');
        this.setData({
          isNoticeExpanded: !this.data.isNoticeExpanded
        });
      },
      handleMaxSummary() {
        this.setData({
          isMaxSummaryExpanded: !this.data.isMaxSummaryExpanded
        });
      }
    }
  });
</script>

<style lang="scss" scoped>
  .action-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #ffffff;
    padding: 32rpx 32rpx;
    box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
    border: 2rpx solid #f0f0f0;
    z-index: 999;
  }
  .collapsed {
    .action-content {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .input-section {
        min-width: 0;
        color: #999999;
        font-size: 28rpx;
        background: #f8f8f8;
        box-sizing: border-box;
        padding: 32rpx 48rpx;
        flex: 1;
        border-radius: 32rpx;
        margin-right: 32rpx;
      }

      .action-buttons {
        display: flex;
        align-items: center;

        .collapsed-btn {
          padding: 16rpx 24rpx;
          border-radius: 36rpx;
          margin-right: 16rpx;
          font-size: 32rpx;
          &.notice {
            background: #fff;
            border: 2rpx solid #e0e0e0;
          }
          &.permission {
            background: #fa3619;
            color: #ffffff;
          }
        }

        .max-avatar {
          width: 56rpx;
          height: 56rpx;

          .avatar-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
          }
        }
      }
    }
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "uploader": "../../../shared/ui/uploader.mpx",
      "t-popup": "tdesign-miniprogram/popup/popup",
      "permission-setting": "./permissionSetting.mpx",
      "max-summary": "./maxSummary.mpx",
      "comment-typing": "./commentTyping.mpx"
    }
  }
</script>